import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:selfeng/features/bookmark/data/datasources/bookmark_firestore_datasource.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class BookmarkFirestoreDatasourceImpl implements BookmarkFirestoreDatasource {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Encodes contentPath to create a valid Firestore document ID
  /// Replaces forward slashes with underscores to prevent nested subcollections
  String _encodeContentPath(String contentPath, {SpeakingStage? stage}) {
    final encodedPath = contentPath.replaceAll('/', '_');
    final stageNum = _getStageNumber(stage);
    return stageNum != null ? '${encodedPath}_stage$stageNum' : encodedPath;
  }

  int? _getStageNumber(SpeakingStage? stage) {
    if (stage == null) return null;
    return switch (stage) {
      SpeakingStage.stage1 => 1,
      SpeakingStage.onboardingStage2 => 2,
      SpeakingStage.stage2 => 2,
      SpeakingStage.onboardingStage3 => 3,
      SpeakingStage.stage3 => 3,
    };
  }

  @override
  Future<Either<AppException, void>> saveBookmark({
    required String contentPath,
    required String section,
    required String title,
    required String level,
    required int chapter,
    SpeakingStage? stage,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Left(
          AppException(
            identifier: 'saveBookmark',
            statusCode: 401,
            message: 'User not authenticated',
          ),
        );
      }

      if (section == 'speaking' && stage == null) {
        return Left(
          AppException(
            identifier: 'saveBookmark',
            statusCode: 400,
            message: 'Stage is required for speaking section',
          ),
        );
      }

      final bookmark = Bookmark(
        id: '', // Will be auto-generated by Firestore
        contentPath: contentPath,
        section: section,
        title: title,
        timestamp: DateTime.now(),
        level: level,
        chapter: chapter,
        stage: stage,
      );

      // Create document ID: encode contentPath to prevent nested subcollections
      final documentId = _encodeContentPath(contentPath, stage: stage);

      await _firestore
          .collection('user-data')
          .doc(user.uid)
          .collection('bookmarks')
          .doc(documentId)
          .set(bookmark.toJson());

      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'saveBookmark',
          statusCode: 0,
          message: 'Failed to save bookmark: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<Bookmark>>> getBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Left(
          AppException(
            identifier: 'getBookmarks',
            statusCode: 401,
            message: 'User not authenticated',
          ),
        );
      }

      Query query = _firestore
          .collection('user-data')
          .doc(user.uid)
          .collection('bookmarks')
          .orderBy('timestamp', descending: true);

      if (section != null && section.isNotEmpty) {
        query = query.where('section', isEqualTo: section);
      }

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.limit(limit).get();

      final bookmarks = snapshot.docs.map((doc) {
        final data = doc.data();
        // Ensure we're working with a proper map
        if (data is Map<String, dynamic>) {
          Map<String, dynamic> jsonData = {...data, 'id': doc.id};
          final stage = jsonData['stage'];
          if (stage is int) {
            String? stageStr = switch (stage) {
              1 => 'stage1',
              2 => 'stage2',
              3 => 'stage3',
              _ => null,
            };
            jsonData['stage'] = stageStr;
          }
          return Bookmark.fromJson(jsonData);
        } else {
          // Fallback in case data is not a map
          return Bookmark.fromJson({
            'id': doc.id,
            'contentPath': '',
            'section': '',
            'title': 'Unknown',
            'timestamp': DateTime.now(),
            'level': '',
            'chapter': 0,
            'stage': null,
            'isBookmarked': true,
          });
        }
      }).toList();

      return Right(bookmarks);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'getBookmarks',
          statusCode: 0,
          message: 'Failed to fetch bookmarks: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<AppException, bool>> isBookmarked(
    String contentPath, {
    SpeakingStage? stage,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Left(
          AppException(
            identifier: 'isBookmarked',
            statusCode: 401,
            message: 'User not authenticated',
          ),
        );
      }

      // Create document ID: encode contentPath to prevent nested subcollections
      final documentId = _encodeContentPath(contentPath, stage: stage);

      final doc = await _firestore
          .collection('user-data')
          .doc(user.uid)
          .collection('bookmarks')
          .doc(documentId)
          .get();

      return Right(doc.exists);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'isBookmarked',
          statusCode: 0,
          message: 'Failed to check bookmark status: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Either<AppException, void>> deleteBookmark(
    String contentPath, {
    SpeakingStage? stage,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Left(
          AppException(
            identifier: 'deleteBookmark',
            statusCode: 401,
            message: 'User not authenticated',
          ),
        );
      }

      // Create document ID: encode contentPath to prevent nested subcollections
      final documentId = _encodeContentPath(contentPath, stage: stage);

      await _firestore
          .collection('user-data')
          .doc(user.uid)
          .collection('bookmarks')
          .doc(documentId)
          .delete();

      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'deleteBookmark',
          statusCode: 0,
          message: 'Failed to delete bookmark: ${e.toString()}',
        ),
      );
    }
  }
}
