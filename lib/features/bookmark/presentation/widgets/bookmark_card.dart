import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/helpers/time_helper.dart';

class BookmarkCard extends StatelessWidget {
  final Bookmark bookmark;

  const BookmarkCard({super.key, required this.bookmark});

  @override
  Widget build(BuildContext context) {
    final sectionIcon = _getSectionIcon(bookmark.section);
    final sectionColor = _getSectionColor(bookmark.section);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Icon(sectionIcon, color: sectionColor),
        title: Text(bookmark.title),
        subtitle: Text(
          '${_formatLevel(bookmark.level)} • Chapter ${bookmark.chapter}',
        ),
        trailing: Text(_formatTimestamp(bookmark.timestamp)),
        onTap: () => _navigateToContent(context),
      ),
    );
  }

  void _navigateToContent(BuildContext context) {
    // Build navigation parameters following the same pattern as ContentTreeItem
    final params = <String, String>{
      'level': bookmark.level,
      'chapter': bookmark.chapter.toString(),
      'path': base64Url.encode(utf8.encode(bookmark.contentPath)),
    };

    // Determine the route based on section, following the same logic as ContentTreeItem
    String route = '';
    switch (bookmark.section) {
      case 'pronunciation':
        route = RouterName.pronunciationChallenge;
        break;
      case 'conversation':
        route = RouterName.conversationVideo;
        break;
      case 'listening':
        route = RouterName.listeningMastery;
        break;
      case 'speaking':
        if (bookmark.stage == null || bookmark.stage == SpeakingStage.stage1) {
          route = RouterName.speakingArena;
        } else {
          route = RouterName.speakingArenaStage;
          // Convert stage to SpeakingStage enum name
          final stageName = _getStageName(bookmark.stage);
          params['stage'] = stageName;
        }
        break;
      default:
        // If section is not recognized, don't navigate
        return;
    }

    // Navigate using the custom navigation helper
    if (route.isNotEmpty) {
      customNav(context, route, params: params);
    }
  }

  String _getStageName(SpeakingStage? stage) {
    if (stage == null) return 'stage2'; // Default fallback
    return switch (stage) {
      SpeakingStage.stage1 => 'stage1',
      SpeakingStage.onboardingStage2 => 'stage2',
      SpeakingStage.stage2 => 'stage2',
      SpeakingStage.onboardingStage3 => 'stage3',
      SpeakingStage.stage3 => 'stage3',
    };
  }

  IconData _getSectionIcon(String section) {
    switch (section) {
      case 'pronunciation':
        return Icons.record_voice_over;
      case 'conversation':
        return Icons.chat;
      case 'listening':
        return Icons.headphones;
      case 'speaking':
        return Icons.mic;
      default:
        return Icons.bookmark;
    }
  }

  Color _getSectionColor(String section) {
    switch (section) {
      case 'pronunciation':
        return const Color(0xFFFF6B6B);
      case 'conversation':
        return const Color(0xFF4ECDC4);
      case 'listening':
        return const Color(0xFF45B7D1);
      case 'speaking':
        return const Color(0xFFE6A742);
      default:
        return Colors.grey;
    }
  }

  String _formatLevel(String level) {
    // Capitalize first letter
    if (level.isEmpty) return level;
    return '${level[0].toUpperCase()}${level.substring(1)}';
  }

  String _formatTimestamp(DateTime timestamp) {
    return TimeHelper.formatTimeAgo(timestamp);
  }
}
