import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

part 'bookmark.freezed.dart';
part 'bookmark.g.dart';

@freezed
sealed class Bookmark with _$Bookmark {
  const factory Bookmark({
    required String id,
    required String contentPath,
    required String section,
    required String title,
    required DateTime timestamp,
    required String level,
    required int chapter,
    SpeakingStage? stage,
    @Default(true) bool isBookmarked,
  }) = _Bookmark;

  factory Bookmark.fromJson(Map<String, dynamic> json) =>
      _$BookmarkFromJson(json);
}
