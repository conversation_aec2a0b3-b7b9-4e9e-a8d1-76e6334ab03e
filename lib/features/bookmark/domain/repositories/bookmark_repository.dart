import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class BookmarkRepository {
  /// Save/toggle bookmark - unified method
  Future<Either<AppException, void>> saveBookmark({
    required String contentPath,
    required String section,
    required String title,
    required String level,
    required int chapter,
    SpeakingStage? stage,
  });

  /// Get paginated bookmarks for bookmark screen
  Future<Either<AppException, List<Bookmark>>> getBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  });

  /// Check if content is bookmarked (for UI state)
  Future<Either<AppException, bool>> isBookmarked(
    String contentPath, {
    SpeakingStage? stage,
  });

  /// Remove bookmark
  Future<Either<AppException, void>> deleteBookmark(
    String contentPath, {
    SpeakingStage? stage,
  });
}
