import 'package:collection/collection.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/bookmark/domain/providers/bookmark_repository_provider.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';
import 'package:selfeng/features/library/domain/providers/library_provider.dart';
import 'package:selfeng/features/library/domain/repositories/library_repository.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_chapter_content_state.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart'; // Assuming ContentIndexData, SectionType
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart'; // Assuming LessonResult, PronunciationScoreParams, SpeakingStage

part 'library_chapter_content_controller.g.dart';

@riverpod
class LibraryChapterContentController
    extends _$LibraryChapterContentController {
  late LibraryRepository libraryRepository;
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository userDataServiceRepository;
  late ChapterContentStateNotifier chapterContentStateNotifier;
  late BookmarkRepository bookmarkRepository;

  @override
  FutureOr<LibraryChapterContentState> build(String level, String chapter) {
    libraryRepository = ref.watch(libraryRepositoryProvider);
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    userDataServiceRepository = ref.watch(userDataServiceProvider);
    chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );
    bookmarkRepository = ref.watch(bookmarkRepositoryProvider);

    // These init methods are called without await, meaning they run asynchronously
    // and update their respective states when complete.
    init(level, chapter);
    initPronunciation(level, chapter);
    initConversation(level, chapter);
    initListening(level, chapter);
    initSpeaking(level, chapter);

    // The controller initially returns an empty state.
    // Subsequent updates will come from the init methods.
    return LibraryChapterContentState();
  }

  // Initializes the main chapter data
  Future<void> init(String level, String chapter) async {
    try {
      final dataResult = await libraryRepository.getChapterData(level, chapter);

      dataResult.fold(
        (failure) {
          // Update controller's own state to reflect this error
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          // Ensure state.value is not null before copying,
          // or handle initial state more carefully if it could be.
          if (state.value != null) {
            state = AsyncData(state.value!.copyWith(chapter: data));
          } else {
            // This case should ideally not happen if build returns a valid initial state.
            // Or, init could be part of an async build method that constructs the initial state.
            state = AsyncData(LibraryChapterContentState(chapter: data));
          }
        },
      );
    } catch (error, stackTrace) {
      // Report error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Library Chapter Data Initialization Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'init',
          'level': level,
          'chapter': chapter,
        },
        fatal: false,
      );
      // Update state with error
      state = AsyncError(error.toString(), stackTrace);
    }
  }

  Future<void> initPronunciation(String level, String chapter) async {
    try {
      final pathIndexFuture = mainLessonRepository.getPathIndex(
        level: level,
        chapter: chapter,
        section: SectionType.pronunciation,
      );
      final scoreFuture = userDataServiceRepository.getPronunciationResult(
        PronunciationScoreParams(level: level, chapter: chapter),
      );

      final results = await Future.wait([pathIndexFuture, scoreFuture]);

      final pathIndexResult =
          results[0] as Either<AppException, List<ContentIndexData>>;
      final scoreResult =
          results[1] as Either<AppException, List<LessonResult>>;

      if (pathIndexResult.isLeft()) {
        final failure = pathIndexResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreResult.isLeft()) {
        final failure = scoreResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }

      final List<ContentIndexData> contentData = pathIndexResult.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData = scoreResult.fold(
        (l) => null,
        (r) => r,
      )!;

      final pathsWithResults = scoreData.map((score) => score.path).toSet();

      // Check bookmark status for each content
      final bookmarkFutures = contentData
          .map(
            (content) => bookmarkRepository.isBookmarked(content.contentPath),
          )
          .toList();
      final bookmarkResults = await Future.wait(bookmarkFutures);
      final bookmarkStatuses = <String, bool>{};
      for (int i = 0; i < contentData.length; i++) {
        final path = contentData[i].contentPath;
        final result = bookmarkResults[i];
        final isBookmarked = result.fold(
          (failure) => false, // Default to false on error
          (value) => value,
        );
        bookmarkStatuses[path] = isBookmarked;
      }

      final updatedContentData = contentData.map((content) {
        return content.copyWith(
          // If the content path is in the results, mark it as having a result
          hasResult: pathsWithResults.contains(content.contentPath),
          isBookmarked: bookmarkStatuses[content.contentPath] ?? false,
        );
      }).toList();

      chapterContentStateNotifier.setPronunciation(updatedContentData);
    } catch (e, stackTrace) {
      // Report error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        e,
        stackTrace,
        reason: 'Pronunciation Content Initialization Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'initPronunciation',
          'level': level,
          'chapter': chapter,
          'section': 'pronunciation',
        },
        fatal: false,
      );
      state = AsyncError(e.toString(), stackTrace);
    }
  }

  Future<void> initConversation(String level, String chapter) async {
    try {
      final pathIndexFuture = mainLessonRepository.getPathIndex(
        level: level,
        chapter: chapter,
        section: SectionType.conversation,
      );
      final scoreFuture = userDataServiceRepository.getConversationResult(
        PronunciationScoreParams(level: level, chapter: chapter),
      );

      final results = await Future.wait([pathIndexFuture, scoreFuture]);
      final pathIndexResult =
          results[0] as Either<AppException, List<ContentIndexData>>;
      final scoreResult =
          results[1] as Either<AppException, List<LessonResult>>;

      if (pathIndexResult.isLeft()) {
        final failure = pathIndexResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreResult.isLeft()) {
        final failure = scoreResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }

      final List<ContentIndexData> contentData = pathIndexResult.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData = scoreResult.fold(
        (l) => null,
        (r) => r,
      )!;

      final pathsWithResults = scoreData.map((score) => score.path).toSet();

      // Check bookmark status for each content
      final bookmarkFutures = contentData
          .map(
            (content) => bookmarkRepository.isBookmarked(content.contentPath),
          )
          .toList();
      final bookmarkResults = await Future.wait(bookmarkFutures);
      final bookmarkStatuses = <String, bool>{};
      for (int i = 0; i < contentData.length; i++) {
        final path = contentData[i].contentPath;
        final result = bookmarkResults[i];
        final isBookmarked = result.fold(
          (failure) => false, // Default to false on error
          (value) => value,
        );
        bookmarkStatuses[path] = isBookmarked;
      }

      final updatedContentData = contentData.map((content) {
        return content.copyWith(
          // If the content path is in the results, mark it as having a result
          hasResult: pathsWithResults.contains(content.contentPath),
          isBookmarked: bookmarkStatuses[content.contentPath] ?? false,
        );
      }).toList();

      chapterContentStateNotifier.setConversation(updatedContentData);
    } catch (e, stackTrace) {
      // Report error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        e,
        stackTrace,
        reason: 'Conversation Content Initialization Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'initConversation',
          'level': level,
          'chapter': chapter,
          'section': 'conversation',
        },
        fatal: false,
      );
      state = AsyncError(e.toString(), stackTrace);
    }
  }

  Future<void> initListening(String level, String chapter) async {
    try {
      final pathIndexFuture = mainLessonRepository.getPathIndex(
        level: level,
        chapter: chapter,
        section: SectionType.listening,
      );
      final scoreFuture = userDataServiceRepository.getListeningResult(
        PronunciationScoreParams(
          level: level,
          chapter: chapter,
        ), // Assuming PronunciationScoreParams is correct here
      );

      final results = await Future.wait([pathIndexFuture, scoreFuture]);
      final pathIndexResult =
          results[0] as Either<AppException, List<ContentIndexData>>;
      final scoreResult =
          results[1] as Either<AppException, List<LessonResult>>;

      if (pathIndexResult.isLeft()) {
        final failure = pathIndexResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreResult.isLeft()) {
        final failure = scoreResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }

      final List<ContentIndexData> contentData = pathIndexResult.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData = scoreResult.fold(
        (l) => null,
        (r) => r,
      )!;

      final pathsWithResults = scoreData.map((score) => score.path).toSet();

      // Check bookmark status for each content
      final bookmarkFutures = contentData
          .map(
            (content) => bookmarkRepository.isBookmarked(content.contentPath),
          )
          .toList();
      final bookmarkResults = await Future.wait(bookmarkFutures);
      final bookmarkStatuses = <String, bool>{};
      for (int i = 0; i < contentData.length; i++) {
        final path = contentData[i].contentPath;
        final result = bookmarkResults[i];
        final isBookmarked = result.fold(
          (failure) => false, // Default to false on error
          (value) => value,
        );
        bookmarkStatuses[path] = isBookmarked;
      }

      final updatedContentData = contentData.map((content) {
        return content.copyWith(
          // If the content path is in the results, mark it as having a result
          hasResult: pathsWithResults.contains(content.contentPath),
          isBookmarked: bookmarkStatuses[content.contentPath] ?? false,
        );
      }).toList();

      chapterContentStateNotifier.setListening(updatedContentData);
    } catch (e, stackTrace) {
      // Report error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        e,
        stackTrace,
        reason: 'Listening Content Initialization Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'initListening',
          'level': level,
          'chapter': chapter,
          'section': 'listening',
        },
        fatal: false,
      );
      state = AsyncError(e.toString(), stackTrace);
    }
  }

  Future<void> initSpeaking(String level, String chapter) async {
    try {
      final pathIndexFuture = mainLessonRepository.getPathIndex(
        level: level,
        chapter: chapter,
        section: SectionType.speaking,
      );
      final scoreStage1Future = userDataServiceRepository.getSpeakingResult(
        level: level,
        chapter: chapter,
        stage: SpeakingStage.stage1,
      );
      final scoreStage2Future = userDataServiceRepository.getSpeakingResult(
        level: level,
        chapter: chapter,
        stage: SpeakingStage.stage2,
      );
      final scoreStage3Future = userDataServiceRepository.getSpeakingResult(
        level: level,
        chapter: chapter,
        stage: SpeakingStage.stage3,
      );

      final allFutureResults = await Future.wait<Either<AppException, dynamic>>(
        [
          pathIndexFuture,
          scoreStage1Future,
          scoreStage2Future,
          scoreStage3Future,
        ],
      );

      final pathIndexResult =
          allFutureResults[0] as Either<AppException, List<ContentIndexData>>;
      final scoreStage1Result =
          allFutureResults[1] as Either<AppException, List<LessonResult>>;
      final scoreStage2Result =
          allFutureResults[2] as Either<AppException, List<LessonResult>>;
      final scoreStage3Result =
          allFutureResults[3] as Either<AppException, List<LessonResult>>;

      // Check for failures in any of the results
      if (pathIndexResult.isLeft()) {
        final failure = pathIndexResult.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreStage1Result.isLeft()) {
        final failure = scoreStage1Result.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreStage2Result.isLeft()) {
        final failure = scoreStage2Result.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }
      if (scoreStage3Result.isLeft()) {
        final failure = scoreStage3Result.fold((l) => l, (r) => null);
        state = AsyncError(failure!.message, StackTrace.current);
        return;
      }

      // Extract data from successful results
      final List<ContentIndexData> contentData = pathIndexResult.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData1 = scoreStage1Result.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData2 = scoreStage2Result.fold(
        (l) => null,
        (r) => r,
      )!;
      final List<LessonResult> scoreData3 = scoreStage3Result.fold(
        (l) => null,
        (r) => r,
      )!;

      // Create sets of paths with results for faster lookup
      final pathsWithResults1 = scoreData1.map((score) => score.path).toSet();
      final pathsWithResults2 = scoreData2.map((score) => score.path).toSet();
      final pathsWithResults3 = scoreData3.map((score) => score.path).toSet();

      // Check bookmark status for each content
      final bookmarkFutures = contentData
          .map(
            (content) => bookmarkRepository.isBookmarked(content.contentPath),
          )
          .toList();
      final bookmarkResults = await Future.wait(bookmarkFutures);
      final bookmarkStatuses = <String, bool>{};
      for (int i = 0; i < contentData.length; i++) {
        final path = contentData[i].contentPath;
        final result = bookmarkResults[i];
        final isBookmarked = result.fold(
          (failure) => false, // Default to false on error
          (value) => value,
        );
        bookmarkStatuses[path] = isBookmarked;
      }

      // Update content data with result information according to specifications
      final updatedContentData = contentData.map<ContentIndexData>((content) {
        // Determine the state for each stage based on current results
        final bool setFirstStage = pathsWithResults1.contains(
          content.contentPath,
        );
        final bool setSecondStage = pathsWithResults2.contains(
          content.contentPath,
        );
        final bool setThirdStage = pathsWithResults3.contains(
          content.contentPath,
        );

        return content.copyWith(
          firstStage: setFirstStage,
          secondStage: setSecondStage,
          thirdStage: setThirdStage,
          isBookmarked: bookmarkStatuses[content.contentPath] ?? false,
        );
      }).toList();

      chapterContentStateNotifier.setSpeaking(updatedContentData);
    } catch (e, stackTrace) {
      // Report error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        e,
        stackTrace,
        reason: 'Speaking Content Initialization Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'initSpeaking',
          'level': level,
          'chapter': chapter,
          'section': 'speaking',
        },
        fatal: false,
      );
      state = AsyncError(e.toString(), stackTrace);
    }
  }

  Future<void> toggleBookmark({
    required String section,
    required String contentPath,
    required String title,
    required String level,
    required int chapter,
    int? stage,
  }) async {
    try {
      // Get current bookmark status from state notifier
      final chapterState = ref.read(chapterContentStateProvider);
      bool currentIsBookmarked = false;
      ContentIndexData? currentContent;
      switch (section.toLowerCase()) {
        case 'pronunciation':
          currentContent = chapterState.pronunciationContent?.firstWhereOrNull(
            (c) => c.contentPath == contentPath,
          );
          currentIsBookmarked = currentContent?.isBookmarked ?? false;
          break;
        case 'conversation':
          currentContent = chapterState.conversationContent?.firstWhereOrNull(
            (c) => c.contentPath == contentPath,
          );
          currentIsBookmarked = currentContent?.isBookmarked ?? false;
          break;
        case 'listening':
          currentContent = chapterState.listeningContent?.firstWhereOrNull(
            (c) => c.contentPath == contentPath,
          );
          currentIsBookmarked = currentContent?.isBookmarked ?? false;
          break;
        case 'speaking':
          currentContent = chapterState.speakingContent?.firstWhereOrNull(
            (c) => c.contentPath == contentPath,
          );
          currentIsBookmarked = currentContent?.isBookmarked ?? false;
          break;
      }

      final result = currentIsBookmarked
          ? await bookmarkRepository.deleteBookmark(
              contentPath,
              stage: stage != null ? SpeakingStage.values[stage] : null,
            )
          : await bookmarkRepository.saveBookmark(
              contentPath: contentPath,
              section: section,
              title: title,
              level: level,
              chapter: chapter,
              stage: stage != null ? SpeakingStage.values[stage] : null,
            );

      result.fold(
        (failure) {
          // Log error
          state = AsyncError(
            'Bookmark operation failed: ${failure.message}',
            StackTrace.current,
          );
        },
        (_) {
          // Update state
          final newStatus = !currentIsBookmarked;
          final chapterState = ref.read(chapterContentStateProvider);
          List<ContentIndexData>? currentList;
          switch (section.toLowerCase()) {
            case 'pronunciation':
              currentList = chapterState.pronunciationContent;
              break;
            case 'conversation':
              currentList = chapterState.conversationContent;
              break;
            case 'listening':
              currentList = chapterState.listeningContent;
              break;
            case 'speaking':
              currentList = chapterState.speakingContent;
              break;
          }
          if (currentList != null) {
            final index = currentList.indexWhere(
              (c) => c.contentPath == contentPath,
            );
            if (index != -1) {
              final updatedList = List<ContentIndexData>.from(currentList);
              updatedList[index] = updatedList[index].copyWith(
                isBookmarked: newStatus,
              );
              switch (section.toLowerCase()) {
                case 'pronunciation':
                  chapterContentStateNotifier.setPronunciation(updatedList);
                  break;
                case 'conversation':
                  chapterContentStateNotifier.setConversation(updatedList);
                  break;
                case 'listening':
                  chapterContentStateNotifier.setListening(updatedList);
                  break;
                case 'speaking':
                  chapterContentStateNotifier.setSpeaking(updatedList);
                  break;
              }
            }
          }
        },
      );
    } catch (e, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        e,
        stackTrace,
        reason: 'Bookmark Toggle Error',
        context: {
          'feature': 'library_chapter_content',
          'operation': 'toggleBookmark',
          'section': section,
          'contentPath': contentPath,
        },
        fatal: false,
      );
      state = AsyncError(e.toString(), stackTrace);
    }
  }
}
