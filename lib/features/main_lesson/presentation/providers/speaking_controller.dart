import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';
import 'package:selfeng/features/bookmark/presentation/providers/bookmark_provider.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

part 'speaking_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our speaking state
@riverpod
class SpeakingController extends _$SpeakingController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  late BookmarkRepository _bookmarkRepository;
  List<ContentIndexData> paths = [];
  Set<String> _stageOneCompletedPaths = {};
  Set<String> _stageTwoCompletedPaths = {};
  Set<String> _stageThreeCompletedPaths = {};
  late MainLessonState _mainLessonState;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  @override
  FutureOr<SpeakingState> build(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _bookmarkRepository = ref.watch(bookmarkRepositoryProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    await mainLessonRepository.isIntro(lessonName: 'speaking').then((val) {
      val.fold(
        (failure) async {
          // Report the error to Crashlytics with context
          final crashlytics = ref.read(crashlyticsServiceProvider);
          await crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason: 'Speaking Intro Check Failed',
                context: {
                  'category': 'speaking_lesson',
                  'operation': 'check_intro',
                  'lesson_name': 'speaking',
                  'level': level,
                  'chapter': chapter,
                  'path': path,
                  'stage': stage?.name ?? 'unknown',
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          // isIntro value is handled in the init method if needed
        },
      );
    });

    // For result screen (stage3), we need to ensure aggregate results are calculated
    // before returning the initial state to prevent UI flicker
    if (stage == SpeakingStage.stage3) {
      // Initialize with a temporary state to prevent null access
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);

      // Calculate aggregate results before returning the state using improved path validation
      await calculateAggregateWithPathValidation(SpeakingStage.stage2);
      await calculateAggregateWithPathValidation(SpeakingStage.stage3);

      // Small delay to ensure state is fully stabilized before UI renders
      await Future.delayed(const Duration(milliseconds: 50));
    } else {
      // For other stages, initialize normally
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);
    }

    return state.value ?? SpeakingState();
  }

  Future<void> init(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
    );
    await mainLessonRepository.saveIntro(lessonName: 'speaking');
    contents.fold(
      (failure) async {
        // Report path index error to Crashlytics
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Path Index Fetch Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'get_path_index',
                'level': level,
                'chapter': chapter,
                'path': path,
                'stage': stage?.name ?? 'unknown',
                'section': 'speaking',
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        paths = data;

        final speakingResultsFutures = [
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage1,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage2,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage3,
          ),
        ];

        final speakingResults = await Future.wait(speakingResultsFutures);

        final completedPaths = speakingResults.map((result) {
          return result.fold((failure) {
            // Report speaking results fetch error (synchronously)
            Future.microtask(() async {
              final crashlytics = ref.read(crashlyticsServiceProvider);
              await crashlytics
                  .recordError(
                    failure,
                    StackTrace.current,
                    reason: 'Speaking Results Fetch Failed',
                    context: {
                      'category': 'speaking_lesson',
                      'operation': 'get_speaking_results',
                      'level': level,
                      'chapter': chapter,
                    },
                    fatal: false,
                  )
                  .catchError((e) => debugPrint('Failed to report error: $e'));
            });

            state = AsyncError(failure.message, StackTrace.current);
            return null;
          }, (data) => data.map((e) => e.path).toSet());
        }).toList();

        if (completedPaths.contains(null)) {
          return;
        }

        _stageOneCompletedPaths = completedPaths[0] ?? <String>{};
        _stageTwoCompletedPaths = completedPaths[1] ?? <String>{};
        _stageThreeCompletedPaths = completedPaths[2] ?? <String>{};

        _mainLessonState = ref.read(mainLessonStateProvider);
        if (_mainLessonState.fromLastCourse == true || path != 'blankpath') {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: false));
        } else {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: true));
        }

        if (_mainLessonState.fromLastCourse == false && path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          final idx = paths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: stage ?? SpeakingStage.stage1,
              ),
            );
          }
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastSpeaking != null) {
          final idx = paths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastSpeaking!.path,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: _mainLessonState.lastSpeaking!.speakingStage,
              ),
            );
          }
        }
        // Log successful initialization
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setCustomKeys({
          'speaking_lesson_level': level,
          'speaking_lesson_chapter': chapter,
          'speaking_lesson_stage': stage?.name ?? 'initial',
          'speaking_paths_count': paths.length,
          'stage1_completed_count': _stageOneCompletedPaths.length,
          'stage2_completed_count': _stageTwoCompletedPaths.length,
          'stage3_completed_count': _stageThreeCompletedPaths.length,
        });
        crashlytics.log(
          'Speaking lesson initialized - Level: $level, Chapter: $chapter, Stage: ${stage?.name ?? "initial"}, Paths: ${paths.length}',
        );

        await initContent();
      },
    );
  }

  Future<void> initContent() async {
    final contentFuture = mainLessonRepository.getSpeakingList(
      paths.map((e) => e.contentPath).toList(),
    );

    // Instead of getting all bookmarks at once, we'll check each content item for the current stage
    final List<Future<Either<AppException, bool>>> bookmarkFutures = [];
    final currentStage = state.value?.speakingStage;

    for (final path in paths) {
      if (currentStage != null) {
        bookmarkFutures.add(
          _bookmarkRepository.isBookmarked(
            path.contentPath,
            stage: currentStage,
          ),
        );
      } else {
        // If we don't have a stage yet, assume it's not bookmarked
        bookmarkFutures.add(Future.value(const Right(false)));
      }
    }

    final results = await Future.wait([
      contentFuture,
      Future.wait(bookmarkFutures),
    ]);

    final contentResult =
        results[0] as Either<AppException, List<SpeakingPart>>;
    final List<Either<AppException, bool>> bookmarkResults =
        results[1] as List<Either<AppException, bool>>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);

      // Report content fetch error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            failure!,
            StackTrace.current,
            reason: 'Speaking Content Fetch Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'get_speaking_content',
              'paths_count': paths.length,
              'error_type': failure.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(failure.message, StackTrace.current);
      return;
    }

    List<SpeakingPart> contentData = contentResult.fold((l) => null, (r) => r)!;

    // Update bookmark status for each content item based on current stage
    for (int i = 0; i < contentData.length; i++) {
      final bookmarkResult = bookmarkResults[i];
      if (bookmarkResult.isRight()) {
        final isBookmarked = bookmarkResult.fold((l) => false, (r) => r);
        contentData[i] = contentData[i].copyWith(isBookmarked: isBookmarked);
      }
      // If there's an error checking bookmark status, we'll just keep the default (false)
    }

    state = AsyncData(state.value!.copyWith(speakings: contentData));

    if (contentData.isNotEmpty) {
      if (state.value?.speakingStage == SpeakingStage.stage2) {
        setQAActive(session: SpeakingSessionType.question);
      } else {
        setQAActive(session: SpeakingSessionType.question, isListening: true);
      }
      saveLastCourse();
    }
  }

  void setQAActive({
    SpeakingSessionType session = SpeakingSessionType.question,
    bool isListening = false,
  }) {
    List<SpeakingPart> tempData = List.from(state.value!.speakings);
    tempData[state.value!.selectedIndex] = tempData[state.value!.selectedIndex]
        .copyWith(
          question: tempData[state.value!.selectedIndex].question.copyWith(
            isActive: session == SpeakingSessionType.question ? true : false,
          ),
          answer: tempData[state.value!.selectedIndex].answer.copyWith(
            isActive: session == SpeakingSessionType.answer ? true : false,
          ),
        );
    state = AsyncData(
      state.value!.copyWith(speakings: tempData, isListening: isListening),
    );
  }

  Future<void> uploadAudio({required String path}) async {
    try {
      // Log audio upload
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.log('Audio uploaded for pronunciation check - Path: $path');
      await crashlytics.setCustomKey(
        'last_audio_upload',
        DateTime.now().toIso8601String(),
      );

      state = AsyncData(
        state.value!.copyWith(audioPath: AudioPath(path: path)),
      );
      checkPronunciation();
    } catch (error, stackTrace) {
      // Report upload error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Audio Upload Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'upload_audio',
              'audio_path': path,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      rethrow;
    }
  }

  Future<void> checkPronunciation() async {
    String text = '';
    switch (state.value?.speakingStage) {
      case SpeakingStage.stage2:
        text = state.value!.speakings[state.value!.selectedIndex].question.text;
        break;
      case SpeakingStage.stage3:
        text = state.value!.speakings[state.value!.selectedIndex].answer.text;
        break;
      default:
    }
    state = AsyncData(state.value!.copyWith(isLoading: true));
    final result = await mainLessonRepository.checkPronunciation(
      audio: state.value!.audioPath!,
      text: text,
    );
    result.fold(
      (failure) async {
        // Report pronunciation check error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Pronunciation Check Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'check_pronunciation',
                'speaking_stage': state.value!.speakingStage.name,
                'text_length': text.length,
                'audio_path': state.value!.audioPath!.path,
                'selected_index': state.value!.selectedIndex,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncData(
          state.value!.copyWith(isLoading: false, response: null),
        );
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        // Log successful pronunciation check
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setCustomKeys({
          'last_pronunciation_check': DateTime.now().toIso8601String(),
          'speaking_stage': state.value!.speakingStage.name,
        });
        crashlytics.log(
          'Pronunciation check completed successfully - Stage: ${state.value!.speakingStage.name}, Score: ${data.pronScore}',
        );

        state = AsyncData(
          state.value!.copyWith(response: data, isLoading: false),
        );

        await saveResult();
      },
    );
  }

  void changeStage(SpeakingStage stage) {
    state = AsyncData(
      state.value!.copyWith(
        speakingStage: stage,
        response: null,
        nextSection: false,
        selectedIndex: 0,
      ),
    );
    if (state.value?.speakingStage == SpeakingStage.stage2) {
      setQAActive(isListening: false);
    } else {
      setQAActive(isListening: true);
    }
  }

  void nextStage() {
    if (state.value?.speakingStage == SpeakingStage.stage1 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage2);
    } else if (state.value?.speakingStage == SpeakingStage.stage2 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage3);
    }
  }

  Future<void> prevQuestion() async {
    if (state.value!.selectedIndex > 0) {
      state = AsyncData(
        state.value!.copyWith(
          selectedIndex: state.value!.selectedIndex - 1,
          response: null,
          // stageTalking: 'stage 1',
        ),
      );
    }
  }

  Future<void> nextQuestion(BuildContext context) async {
    try {
      SpeakingStage speakingStage = state.value!.speakingStage;
      if (state.value!.selectedIndex < state.value!.speakings.length - 1) {
        state = AsyncData(
          state.value!.copyWith(
            selectedIndex: state.value!.selectedIndex + 1,
            response: null,
            // stageTalking: 'stage 1',
          ),
        );
        if (speakingStage == SpeakingStage.stage1) {
          saveStageOne();
        }
        if (speakingStage == SpeakingStage.stage2) {
          setQAActive(session: SpeakingSessionType.question);
        } else {
          setQAActive(session: SpeakingSessionType.question, isListening: true);
        }
        if (speakingStage != SpeakingStage.stage1) {
          // Save the current state before navigation
          await saveLastCourse();
          // Set fromLastCourse to true so the next screen uses lastSpeaking data
          final mainLessonStateNotifier = ref.watch(
            mainLessonStateProvider.notifier,
          );
          mainLessonStateNotifier.updateFromLastCourse(true);

          customNav(
            context,
            RouterName.speakingArenaStage,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        } else {
          saveLastCourse();
        }
      } else {
        state = AsyncData(state.value!.copyWith(nextSection: true));
        if (state.value?.speakingStage == SpeakingStage.stage3) {
          await calculateAggregateWithPathValidation(SpeakingStage.stage2);
          await calculateAggregateWithPathValidation(SpeakingStage.stage3);
          customNav(
            context,
            RouterName.speakingArenaResult,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        } else {
          saveStageOne();
          nextStage();
          customNav(
            context,
            RouterName.speakingArenaStage,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        }
      }
    } catch (error, stackTrace) {
      // Report navigation/next question error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Next Question Navigation Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'next_question',
              'speaking_stage': state.value?.speakingStage.name ?? 'unknown',
              'selected_index': state.value?.selectedIndex ?? -1,
              'total_speakings': state.value?.speakings.length ?? 0,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      rethrow;
    }
  }

  Future<void> saveLastCourse() async {
    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.speaking,
      path: paths[state.value!.selectedIndex].contentPath,
      speakingStage: state.value!.speakingStage,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.speaking,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastSpeaking(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> calculateAgregate(SpeakingStage stage) async {
    final result = await _userDataServiceRepository.calculateSpeakingResult(
      level: level,
      chapter: chapter,
      stage: stage,
    );

    result.fold(
      (failure) async {
        // Report aggregate calculation error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Aggregate Calculation Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'calculate_aggregate',
                'stage': stage.name,
                'level': level,
                'chapter': chapter,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        if (stage == SpeakingStage.stage2) {
          state = AsyncData(state.value!.copyWith(resultStage2: data));
        } else if (stage == SpeakingStage.stage3) {
          state = AsyncData(state.value!.copyWith(resultStage3: data));
        }
      },
    );
  }

  Future<void> saveResult() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: state.value!.speakingStage,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': state.value!.response!.accuracyScore,
          'fluencyScore': state.value!.response!.fluencyScore,
          'prosodyScore': state.value!.response!.prosodyScore,
          'completenessScore': state.value!.response!.completenessScore,
          'pronScore': state.value!.response!.pronScore,
        },
      ),
    );

    result.fold(
      (failure) async {
        // Report save result error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Result Save Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'save_result',
                'level': level,
                'chapter': chapter,
                'speaking_stage': state.value!.speakingStage.name,
                'content_order': paths[state.value!.selectedIndex].contentOrder,
                'path': paths[state.value!.selectedIndex].contentPath,
                'accuracy_score': state.value!.response!.accuracyScore,
                'fluency_score': state.value!.response!.fluencyScore,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        // Log successful result save
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics.log(
          'Speaking result saved - Stage: ${state.value!.speakingStage.name}, Path: ${paths[state.value!.selectedIndex].contentPath}',
        );

        if (state.value!.speakingStage == SpeakingStage.stage2) {
          _stageTwoCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
          // Track stage 2 completion
          await crashlytics.setCustomKey(
            'stage2_completions',
            _stageTwoCompletedPaths.length,
          );
        } else if (state.value!.speakingStage == SpeakingStage.stage3) {
          _stageThreeCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
          // Track stage 3 completion
          await crashlytics.setCustomKey(
            'stage3_completions',
            _stageThreeCompletedPaths.length,
          );
        }
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> saveStageOne() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: SpeakingStage.stage1,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': 0,
          'fluencyScore': 0,
          'prosodyScore': 0,
          'completenessScore': 0,
          'pronScore': 0,
        },
      ),
    );

    result.fold(
      (failure) async {
        // Report save stage one error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Stage One Save Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'save_stage_one',
                'level': level,
                'chapter': chapter,
                'content_order': paths[state.value!.selectedIndex].contentOrder,
                'path': paths[state.value!.selectedIndex].contentPath,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        _stageOneCompletedPaths.add(
          paths[state.value!.selectedIndex].contentPath,
        );
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m) for each stage
    // Create a set of required paths and check if all stages have completed all paths
    final requiredPaths = paths.map((pathData) => pathData.contentPath).toSet();

    final stageOneComplete = requiredPaths
        .difference(_stageOneCompletedPaths)
        .isEmpty;
    final stageTwoComplete = requiredPaths
        .difference(_stageTwoCompletedPaths)
        .isEmpty;
    final stageThreeComplete = requiredPaths
        .difference(_stageThreeCompletedPaths)
        .isEmpty;

    if (stageOneComplete && stageTwoComplete && stageThreeComplete) {
      // Log section completion
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.log(
        'Speaking section completed - Level: $level, Chapter: $chapter',
      );
      await crashlytics.setCustomKeys({
        'last_section_completed': 'speaking',
        'completed_level': level,
        'completed_chapter': chapter,
        'section_completed_at': DateTime.now().toIso8601String(),
      });

      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.speaking,
      );
    }
  }

  void updateHasResult() {
    _chapterContentStateNotifier.updateSpeaking(
      paths[state.value!.selectedIndex],
      state.value!.speakingStage,
    );
  }

  void clearResponse() {
    state = AsyncData(state.value!.copyWith(response: null));
  }

  Future<void> saveBookmark() async {
    try {
      // Get the current bookmark status
      final contentPath = paths[state.value!.selectedIndex].contentPath;
      final currentStage = state.value!.speakingStage;

      final bookmarkCheckResult = await _bookmarkRepository.isBookmarked(
        contentPath,
        stage: currentStage, // Using index as the stage identifier
      );

      bookmarkCheckResult.fold(
        (failure) async {
          // Report bookmark save error
          final crashlytics = ref.read(crashlyticsServiceProvider);
          await crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason: 'Speaking Bookmark Save Failed',
                context: {
                  'category': 'speaking_lesson',
                  'operation': 'isBookmarked',
                  'section': 'speaking',
                  'path': contentPath,
                  'stage': currentStage.name,
                  'error_type': failure.runtimeType.toString(),
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.empty);
        },
        (isCurrentlyBookmarked) async {
          if (isCurrentlyBookmarked) {
            // Remove bookmark
            final deleteResult = await _bookmarkRepository.deleteBookmark(
              contentPath,
              stage: currentStage,
            );
            deleteResult.fold(
              (failure) async {
                // Report bookmark delete error
                final crashlytics = ref.read(crashlyticsServiceProvider);
                await crashlytics
                    .recordError(
                      failure,
                      StackTrace.current,
                      reason: 'Speaking Bookmark Delete Failed',
                      context: {
                        'category': 'speaking_lesson',
                        'operation': 'deleteBookmark',
                        'section': 'speaking',
                        'path': contentPath,
                        'stage': currentStage.name,
                        'error_type': failure.runtimeType.toString(),
                      },
                      fatal: false,
                    )
                    .catchError(
                      (e) => debugPrint('Failed to report error: $e'),
                    );

                state = AsyncError(failure.message, StackTrace.empty);
              },
              (data) {
                // Successfully removed bookmark
                var speakings = state.value!.speakings;
                speakings = List.from(speakings);
                speakings[state.value!.selectedIndex] =
                    speakings[state.value!.selectedIndex].copyWith(
                      isBookmarked: false,
                    );
                // Update the current state with the new bookmark status
                state = AsyncData(state.value!.copyWith(speakings: speakings));
                paths[state.value!.selectedIndex] =
                    paths[state.value!.selectedIndex].copyWith(
                      isBookmarked: false,
                    );
                _chapterContentStateNotifier.updateSpeaking(
                  paths[state.value!.selectedIndex],
                  state.value!.speakingStage,
                );
              },
            );
          } else {
            // Add bookmark
            // Get the title from the current content
            final title =
                state.value!.speakings[state.value!.selectedIndex].title;

            final saveResult = await _bookmarkRepository.saveBookmark(
              contentPath: contentPath,
              section: 'speaking',
              title: title,
              level: level,
              chapter: int.parse(chapter),
              stage: currentStage, // Include stage for speaking content
            );

            saveResult.fold(
              (failure) async {
                // Report bookmark save error
                final crashlytics = ref.read(crashlyticsServiceProvider);
                await crashlytics
                    .recordError(
                      failure,
                      StackTrace.current,
                      reason: 'Speaking Bookmark Save Failed',
                      context: {
                        'category': 'speaking_lesson',
                        'operation': 'saveBookmark',
                        'section': 'speaking',
                        'path': contentPath,
                        'stage': currentStage.name,
                        'error_type': failure.runtimeType.toString(),
                      },
                      fatal: false,
                    )
                    .catchError(
                      (e) => debugPrint('Failed to report error: $e'),
                    );

                state = AsyncError(failure.message, StackTrace.empty);
              },
              (data) {
                // Successfully added bookmark
                var speakings = state.value!.speakings;
                speakings = List.from(speakings);
                speakings[state.value!.selectedIndex] =
                    speakings[state.value!.selectedIndex].copyWith(
                      isBookmarked: true,
                    );
                // Update the current state with the new bookmark status
                state = AsyncData(state.value!.copyWith(speakings: speakings));
                paths[state.value!.selectedIndex] =
                    paths[state.value!.selectedIndex].copyWith(
                      isBookmarked: true,
                    );
                _chapterContentStateNotifier.updateSpeaking(
                  paths[state.value!.selectedIndex],
                  state.value!.speakingStage,
                );
              },
            );
          }
        },
      );
    } catch (error, stackTrace) {
      // Report unexpected bookmark save error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Unexpected Speaking Bookmark Save Error',
            context: {
              'category': 'speaking_lesson',
              'operation': 'saveBookmark',
              'section': 'speaking',
              'page_index': state.value!.selectedIndex.toString(),
              'stage': state.value?.speakingStage.name ?? 'unknown',
              'error_type': error.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(error.toString(), stackTrace);
    }
  }

  void translate() {
    state = AsyncData(
      state.value!.copyWith(isTranslate: !state.value!.isTranslate),
    );
  }

  /// Public getters for accessing completed paths data
  Set<String> get stageOneCompletedPaths => _stageOneCompletedPaths;
  Set<String> get stageTwoCompletedPaths => _stageTwoCompletedPaths;
  Set<String> get stageThreeCompletedPaths => _stageThreeCompletedPaths;

  /// Centralized stage completion detection methods

  /// Get the set of required content paths for this lesson
  Set<String> get requiredContentPaths =>
      paths.map((pathData) => pathData.contentPath).toSet();

  /// Check if a specific stage is complete using path-based validation
  bool isStageComplete(SpeakingStage stage) {
    final requiredPaths = requiredContentPaths;

    switch (stage) {
      case SpeakingStage.stage1:
        return requiredPaths.difference(_stageOneCompletedPaths).isEmpty;
      case SpeakingStage.stage2:
        return requiredPaths.difference(_stageTwoCompletedPaths).isEmpty;
      case SpeakingStage.stage3:
        return requiredPaths.difference(_stageThreeCompletedPaths).isEmpty;
      default:
        return false;
    }
  }

  /// Check if both stage 2 and stage 3 are complete (for success content display)
  bool areBothMainStagesComplete() {
    return isStageComplete(SpeakingStage.stage2) &&
        isStageComplete(SpeakingStage.stage3);
  }

  /// Get missing paths for a specific stage (for debugging/validation)
  Set<String> getMissingPathsForStage(SpeakingStage stage) {
    final requiredPaths = requiredContentPaths;

    switch (stage) {
      case SpeakingStage.stage1:
        return requiredPaths.difference(_stageOneCompletedPaths);
      case SpeakingStage.stage2:
        return requiredPaths.difference(_stageTwoCompletedPaths);
      case SpeakingStage.stage3:
        return requiredPaths.difference(_stageThreeCompletedPaths);
      default:
        return requiredPaths;
    }
  }

  /// Validate that aggregate score data is consistent with content paths
  bool isScoreDataValid(SpeakingAgregateScore? score, SpeakingStage stage) {
    if (score == null) return false;

    final requiredPaths = requiredContentPaths;
    final completedPaths = switch (stage) {
      SpeakingStage.stage2 => _stageTwoCompletedPaths,
      SpeakingStage.stage3 => _stageThreeCompletedPaths,
      _ => <String>{},
    };

    // Score should only be calculated from results whose paths exist in content paths
    // The dataCount should match the number of completed paths that exist in required paths
    final validCompletedPaths = completedPaths.intersection(requiredPaths);
    return score.dataCount == validCompletedPaths.length;
  }

  /// Improved score calculation that filters results by valid content paths
  Future<void> calculateAggregateWithPathValidation(SpeakingStage stage) async {
    try {
      // Get all results for the stage
      final resultsEither = await _userDataServiceRepository.getSpeakingResult(
        level: level,
        chapter: chapter,
        stage: stage,
      );

      await resultsEither.fold(
        (failure) async {
          // Report aggregate calculation error
          final crashlytics = ref.read(crashlyticsServiceProvider);
          await crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason:
                    'Speaking Aggregate Calculation Failed (Path Validated)',
                context: {
                  'category': 'speaking_lesson',
                  'operation': 'calculate_aggregate_path_validated',
                  'stage': stage.name,
                  'level': level,
                  'chapter': chapter,
                  'error_type': failure.runtimeType.toString(),
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.current);
        },
        (results) async {
          final requiredPaths = requiredContentPaths;

          // Filter results to only include those whose paths exist in content paths
          final validResults = results
              .where((result) => requiredPaths.contains(result.path))
              .toList();

          // Calculate aggregate score from valid results only
          final aggregateScore = _calculateAggregateFromResults(validResults);

          // Log the filtering for debugging
          final crashlytics = ref.read(crashlyticsServiceProvider);
          await crashlytics.setCustomKeys({
            'stage_${stage.name}_total_results': results.length,
            'stage_${stage.name}_valid_results': validResults.length,
            'stage_${stage.name}_filtered_count':
                results.length - validResults.length,
          });

          if (results.length != validResults.length) {
            crashlytics.log(
              'Filtered speaking results - Stage: ${stage.name}, '
              'Total: ${results.length}, Valid: ${validResults.length}, '
              'Filtered out: ${results.length - validResults.length}',
            );
          }

          // Update state with the calculated score
          if (stage == SpeakingStage.stage2) {
            state = AsyncData(
              state.value!.copyWith(resultStage2: aggregateScore),
            );
          } else if (stage == SpeakingStage.stage3) {
            state = AsyncData(
              state.value!.copyWith(resultStage3: aggregateScore),
            );
          }
        },
      );
    } catch (error, stackTrace) {
      // Report unexpected errors
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Unexpected error in path-validated aggregate calculation',
            context: {
              'category': 'speaking_lesson',
              'operation': 'calculate_aggregate_path_validated',
              'stage': stage.name,
              'level': level,
              'chapter': chapter,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(error.toString(), stackTrace);
    }
  }

  /// Calculate aggregate score from a list of lesson results
  SpeakingAgregateScore _calculateAggregateFromResults(
    List<LessonResult> results,
  ) {
    if (results.isEmpty) {
      return const SpeakingAgregateScore(
        accuracyScore: 0,
        fluencyScore: 0,
        prosodyScore: 0,
        completenessScore: 0,
        pronScore: 0,
        dataCount: 0,
      );
    }

    final scores = results.map((result) => result.result).toList();

    double sumScore(String key) => scores
        .map((score) => score[key] as double)
        .fold<double>(0, (currentSum, score) => currentSum + score);

    return SpeakingAgregateScore(
      accuracyScore: sumScore('accuracyScore'),
      fluencyScore: sumScore('fluencyScore'),
      prosodyScore: sumScore('prosodyScore'),
      completenessScore: sumScore('completenessScore'),
      pronScore: sumScore('pronScore'),
      dataCount: scores.length,
    );
  }
}
