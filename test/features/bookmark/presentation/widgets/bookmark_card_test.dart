import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/bookmark/presentation/widgets/bookmark_card.dart';

void main() {
  group('BookmarkCard', () {
    testWidgets('should display bookmark information correctly', (
      tester,
    ) async {
      // Arrange
      final bookmark = Bookmark(
        id: '1',
        contentPath: 'test/path',
        section: 'pronunciation',
        title: 'Test Bookmark',
        timestamp: DateTime.now(),
        level: 'beginner',
        chapter: 1,
        stage: null,
        isBookmarked: true,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: BookmarkCard(bookmark: bookmark)),
        ),
      );

      // Assert
      expect(find.text('Test Bookmark'), findsOneWidget);
      expect(find.text('Beginner • Chapter 1'), findsOneWidget);
      expect(find.byIcon(Icons.record_voice_over), findsOneWidget);
    });

    testWidgets('should display speaking bookmark with stage correctly', (
      tester,
    ) async {
      // Arrange
      final bookmark = Bookmark(
        id: '2',
        contentPath: 'test/speaking/path',
        section: 'speaking',
        title: 'Speaking Test',
        timestamp: DateTime.now(),
        level: 'intermediate',
        chapter: 2,
        stage: SpeakingStage.stage2,
        isBookmarked: true,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: BookmarkCard(bookmark: bookmark)),
        ),
      );

      // Assert
      expect(find.text('Speaking Test'), findsOneWidget);
      expect(find.text('Intermediate • Chapter 2'), findsOneWidget);
      expect(find.byIcon(Icons.mic), findsOneWidget);
    });

    testWidgets('should handle different sections correctly', (tester) async {
      final sections = [
        ('conversation', Icons.chat),
        ('listening', Icons.headphones),
        ('speaking', Icons.mic),
        ('pronunciation', Icons.record_voice_over),
      ];

      for (final (section, expectedIcon) in sections) {
        final bookmark = Bookmark(
          id: section,
          contentPath: 'test/$section/path',
          section: section,
          title: '$section Test',
          timestamp: DateTime.now(),
          level: 'advanced',
          chapter: 3,
          stage: section == 'speaking' ? SpeakingStage.stage1 : null,
          isBookmarked: true,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: BookmarkCard(bookmark: bookmark)),
          ),
        );

        expect(find.byIcon(expectedIcon), findsOneWidget);
        await tester.pumpAndSettle();
      }
    });
  });
}
